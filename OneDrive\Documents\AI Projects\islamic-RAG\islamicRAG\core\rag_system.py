"""Main RAG system orchestrator for Islamic texts."""

from typing import List, Dict, Any, Optional
from loguru import logger

from .config import Config
from ..embeddings.embedding_manager import EmbeddingManager
from ..vector_store.qdrant_store import QdrantVectorStore
from ..chunking.islamic_chunker import IslamicChunker
from ..llm.gemini_client import GeminiClient
from ..data_pipeline.pipeline import DataPipeline


class IslamicRAG:
    """Main Islamic RAG system class."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the Islamic RAG system."""
        self.config = Config(config_path)
        self.embedding_manager = None
        self.vector_store = None
        self.chunker = None
        self.llm_client = None
        self.data_pipeline = None
        self._initialized = False
        
    def initialize(self) -> None:
        """Initialize all system components."""
        try:
            logger.info("Initializing Islamic RAG system...")
            
            # Initialize embedding manager
            self.embedding_manager = EmbeddingManager(self.config.embeddings)
            
            # Initialize vector store
            self.vector_store = QdrantVectorStore(self.config.vector_store)
            
            # Initialize chunker
            self.chunker = IslamicChunker(self.config.chunking)
            
            # Initialize LLM client
            self.llm_client = GeminiClient(
                api_key=self.config.api_keys.gemini_api_key,
                config=self.config.llm
            )
            
            # Initialize data pipeline
            self.data_pipeline = DataPipeline(
                chunker=self.chunker,
                embedding_manager=self.embedding_manager,
                vector_store=self.vector_store,
                config=self.config
            )
            
            self._initialized = True
            logger.info("Islamic RAG system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Islamic RAG system: {e}")
            raise
    
    def ingest_data(self, data_path: str, data_type: str) -> None:
        """Ingest Islamic texts into the system."""
        if not self._initialized:
            raise RuntimeError("System not initialized. Call initialize() first.")
            
        self.data_pipeline.ingest(data_path, data_type)
    
    def query(self, question: str, top_k: int = 5) -> Dict[str, Any]:
        """Query the Islamic RAG system."""
        if not self._initialized:
            raise RuntimeError("System not initialized. Call initialize() first.")
            
        try:
            # Retrieve relevant documents
            relevant_docs = self.retrieve(question, top_k)
            
            # Generate response
            response = self.generate_response(question, relevant_docs)
            
            return {
                "question": question,
                "answer": response,
                "sources": relevant_docs,
                "metadata": {
                    "num_sources": len(relevant_docs),
                    "model": self.config.llm.model_name
                }
            }
            
        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise
    
    def retrieve(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for a query."""
        # Generate query embedding
        query_embedding = self.embedding_manager.embed_text(query)
        
        # Search vector store
        results = self.vector_store.search(query_embedding, top_k)
        
        return results
    
    def generate_response(self, question: str, context_docs: List[Dict[str, Any]]) -> str:
        """Generate response using LLM with retrieved context."""
        # Prepare context
        context = self._prepare_context(context_docs)
        
        # Generate response
        response = self.llm_client.generate_response(question, context)
        
        return response
    
    def _prepare_context(self, docs: List[Dict[str, Any]]) -> str:
        """Prepare context from retrieved documents."""
        context_parts = []
        
        for i, doc in enumerate(docs, 1):
            content = doc.get('content', '')
            source = doc.get('metadata', {}).get('source', 'Unknown')
            
            context_parts.append(f"Source {i} ({source}):\n{content}\n")
        
        return "\n".join(context_parts)
    
    def health_check(self) -> Dict[str, Any]:
        """Perform system health check."""
        status = {
            "initialized": self._initialized,
            "components": {}
        }
        
        if self._initialized:
            status["components"] = {
                "embedding_manager": self.embedding_manager.health_check(),
                "vector_store": self.vector_store.health_check(),
                "llm_client": self.llm_client.health_check()
            }
        
        return status