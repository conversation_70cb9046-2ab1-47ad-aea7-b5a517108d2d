"""Configuration management for Islamic RAG system."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

load_dotenv()


class APIKeysConfig(BaseModel):
    gemini_api_key: str = Field(..., description="Google Gemini API key")


class VectorStoreConfig(BaseModel):
    provider: str = Field(default="qdrant", description="Vector store provider")
    url: str = Field(..., description="Qdrant Cloud endpoint URL")
    api_key: str = Field(..., description="Qdrant Cloud API key")
    collection_name: str = Field(default="islamic_texts", description="Collection name")
    vector_size: int = Field(default=768, description="Vector dimension size")
    distance_metric: str = Field(default="cosine", description="Distance metric")


class EmbeddingsConfig(BaseModel):
    model_name: str = Field(
        default="all-MiniLM-L6-v2",
        description="Embedding model name"
    )
    batch_size: int = Field(default=32, description="Batch size for embeddings")
    max_length: int = Field(default=512, description="Maximum sequence length")


class LLMConfig(BaseModel):
    model_name: str = Field(default="gemini-1.5-pro", description="LLM model name")
    temperature: float = Field(default=0.3, description="Generation temperature")
    max_tokens: int = Field(default=2048, description="Maximum tokens to generate")
    system_prompt: str = Field(..., description="System prompt for the LLM")


class ChunkingConfig(BaseModel):
    strategy: str = Field(default="islamic_aware", description="Chunking strategy")
    chunk_size: int = Field(default=512, description="Target chunk size")
    chunk_overlap: int = Field(default=50, description="Overlap between chunks")
    respect_verse_boundaries: bool = Field(default=True, description="Respect verse boundaries")
    respect_hadith_structure: bool = Field(default=True, description="Respect hadith structure")


class Config:
    """Configuration manager for Islamic RAG system."""
    
    def __init__(self, config_path: Optional[str] = None):
        # Resolve the config path relative to the project root
        if config_path:
            self.config_path = Path(config_path)
        else:
            # Go up from islamicRAG/core/config.py to project root, then to config/config.yaml
            current_file_path = Path(__file__).parent.parent.parent.resolve()
            self.config_path = current_file_path / "config" / "config.yaml"

        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file and environment variables."""
        config = {}
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        
        # Override with environment variables
        # Ensure the nested dictionaries exist before trying to access them
        config.setdefault('api_keys', {})
        config.setdefault('vector_store', {})
        
        # Override GEMINI_API_KEY
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            config['api_keys']['gemini_api_key'] = gemini_api_key

        # Override Qdrant URL and API Key
        qdrant_url = os.getenv('QDRANT_URL')
        if qdrant_url:
            config['vector_store']['url'] = qdrant_url
            
        qdrant_api_key = os.getenv('QDRANT_API_KEY')
        if qdrant_api_key:
            config['vector_store']['api_key'] = qdrant_api_key
        
        return config
    
    @property
    def api_keys(self) -> APIKeysConfig:
        return APIKeysConfig(**self._config.get('api_keys', {}))
    
    @property
    def vector_store(self) -> VectorStoreConfig:
        return VectorStoreConfig(**self._config.get('vector_store', {}))
    
    @property
    def embeddings(self) -> EmbeddingsConfig:
        return EmbeddingsConfig(**self._config.get('embeddings', {}))
    
    @property
    def llm(self) -> LLMConfig:
        return LLMConfig(**self._config.get('llm', {}))
    
    @property
    def chunking(self) -> ChunkingConfig:
        return ChunkingConfig(**self._config.get('chunking', {}))
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        return self._config.get(key, default)
    
if __name__ == "__main__":
    cfg = Config()
    print("Loaded config:", cfg._config)
    print("Gemini API Key from property:", cfg.api_keys.gemini_api_key)
    print("Gemini API Key from raw config:", cfg._config.get('api_keys', {}).get('gemini_api_key'))
