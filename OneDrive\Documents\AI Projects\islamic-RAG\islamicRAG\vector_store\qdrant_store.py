"""Qdrant vector store implementation for Islamic texts."""

from typing import List, Dict, Any, Optional
import uuid
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from loguru import logger

from ..core.config import VectorStoreConfig


class QdrantVectorStore:
    """Qdrant vector store for Islamic texts."""
    
    def __init__(self, config: VectorStoreConfig):
        self.config = config
        self.client = None
        self.collection_name = config.collection_name
        self._connect()
        self._ensure_collection()
    
    def _connect(self) -> None:
        """Connect to Qdrant instance."""
        try:
            self.client = QdrantClient(
                url = self.config.url,
                api_key=self.config.api_key
            )
            logger.info(f"Connected to Qdrant at {self.config.url}")
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {e}")
            raise
    
    def _ensure_collection(self) -> None:
        """Ensure the collection exists."""
        try:
            collections = self.client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.config.vector_size,
                        distance=Distance.COSINE if self.config.distance_metric == "cosine" else Distance.EUCLID
                    )
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection {self.collection_name} already exists")
                
        except Exception as e:
            logger.error(f"Failed to ensure collection: {e}")
            raise
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> List[str]:
        """Add documents to the vector store."""
        try:
            points = []
            doc_ids = []
            
            for doc in documents:
                doc_id = str(uuid.uuid4())
                doc_ids.append(doc_id)
                
                point = PointStruct(
                    id=doc_id,
                    vector=doc["embedding"],
                    payload={
                        "content": doc["content"],
                        "metadata": doc.get("metadata", {})
                    }
                )
                points.append(point)
            
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Added {len(documents)} documents to vector store")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise
    
    def search(self, query_vector: List[float], top_k: int = 5, filter_conditions: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """Search for similar documents."""
        try:
            search_filter = None
            if filter_conditions:
                # Build Qdrant filter from conditions
                search_filter = self._build_filter(filter_conditions)
            
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=top_k,
                query_filter=search_filter
            )
            
            documents = []
            for result in results:
                doc = {
                    "id": result.id,
                    "content": result.payload["content"],
                    "metadata": result.payload["metadata"],
                    "score": result.score
                }
                documents.append(doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    def _build_filter(self, conditions: Dict) -> Filter:
        """Build Qdrant filter from conditions."""
        # Implement filter building logic based on your needs
        # This is a basic example
        field_conditions = []
        
        for field, value in conditions.items():
            field_conditions.append(
                FieldCondition(
                    key=f"metadata.{field}",
                    match=MatchValue(value=value)
                )
            )
        
        return Filter(must=field_conditions)
    
    def delete_documents(self, doc_ids: List[str]) -> None:
        """Delete documents by IDs."""
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=doc_ids
            )
            logger.info(f"Deleted {len(doc_ids)} documents")
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            raise
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get collection information."""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "name": info.config.params.vectors.size,
                "vectors_count": info.vectors_count,
                "points_count": info.points_count,
                "status": info.status
            }
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {}
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on vector store."""
        try:
            # Test connection
            collections = self.client.get_collections()
            collection_info = self.get_collection_info()
            
            return {
                "status": "healthy",
                "url": self.config.url,
                "collection_name": self.collection_name,
                "collection_info": collection_info
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }