# Islamic RAG System Configuration

# API Keys
api_keys:
  gemini_api_key: "AIzaSyDhH7ROWO1AQnFW85ckFRJo74vNcNuW2Kc"
  
# Vector Store Configuration
vector_store:
  provider: "qdrant"
  url: "https://a5f8cfd9-6bc8-4d0c-adc8-84566cdf17ff.us-west-2-0.aws.cloud.qdrant.io" 
  api_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.WeQrwiWI__OaBpeNfCVAtKJiOz4f--ik9mnm8JTOHk0" 
  collection_name: "islamic_texts"
  vector_size: 768
  distance_metric: "cosine"
    
# Embedding Configuration
embeddings:
  model_name: "sentence-transformers/all-MiniLM-L6-v2"
  batch_size: 32
  max_length: 512
  
# LLM Configuration
llm:
  model_name: "gemini-1.5-pro"
  temperature: 0.3
  max_tokens: 2048
  system_prompt: |
    You are an Islamic knowledge assistant. Provide accurate, respectful responses 
    based on authentic Islamic sources. Always cite your sources when referencing 
    Quran verses or Hadith. Do not answer questions that are not related to Islam.
    Also never answer the question you don't know.

# Chunking Configuration
chunking:
  strategy: "islamic_aware"
  chunk_size: 512
  chunk_overlap: 50
  respect_verse_boundaries: true
  respect_hadith_structure: true
  
# Data Pipeline
data_pipeline:
  supported_formats: ["json", "txt", "csv", "xml"]
  batch_size: 100
  validation_enabled: true
  
# Logging
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_path: "logs/islamic_rag.log"
  
# Performance
performance:
  max_concurrent_requests: 10
  cache_size: 1000
  timeout_seconds: 30