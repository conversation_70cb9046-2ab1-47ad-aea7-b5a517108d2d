"""FastAPI application for Islamic RAG system."""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
from loguru import logger

from ..core.rag_system import IslamicRAG


# Pydantic models
class QueryRequest(BaseModel):
    question: str
    top_k: int = 5
    filters: Optional[Dict[str, Any]] = None


class QueryResponse(BaseModel):
    question: str
    answer: str
    sources: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class IngestRequest(BaseModel):
    data_path: str
    data_type: str


class HealthResponse(BaseModel):
    status: str
    components: Dict[str, Any]


# Initialize FastAPI app
app = FastAPI(
    title="Islamic RAG API",
    description="Production-ready RAG system for Islamic texts",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global RAG system instance
rag_system: Optional[IslamicRAG] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the RAG system on startup."""
    global rag_system
    try:
        logger.info("Initializing Islamic RAG system...")
        rag_system = IslamicRAG()
        rag_system.initialize()
        logger.info("Islamic RAG system initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        raise


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        health_status = rag_system.health_check()
        return HealthResponse(**health_status)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query", response_model=QueryResponse)
async def query_rag(request: QueryRequest):
    """Query the Islamic RAG system."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        result = rag_system.query(
            question=request.question,
            top_k=request.top_k
        )
        return QueryResponse(**result)
    except Exception as e:
        logger.error(f"Query failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ingest")
async def ingest_data(request: IngestRequest, background_tasks: BackgroundTasks):
    """Ingest data into the RAG system."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        # Run ingestion in background
        background_tasks.add_task(
            rag_system.ingest_data,
            request.data_path,
            request.data_type
        )
        
        return {"message": "Data ingestion started", "status": "processing"}
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/collection/info")
async def get_collection_info():
    """Get vector store collection information."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        info = rag_system.vector_store.get_collection_info()
        return info
    except Exception as e:
        logger.error(f"Failed to get collection info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    uvicorn.run(
        "islamicRAG.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False
    )