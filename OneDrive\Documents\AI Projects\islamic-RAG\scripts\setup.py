#!/usr/bin/env python3
"""Setup script for Islamic RAG system."""

import shutil
from pathlib import Path


def setup_directories():
    """Create necessary directories."""
    directories = [
        "logs",
        "data",
        "data/quran",
        "data/hadith",
        "data/scholarly_texts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")


def setup_config():
    """Setup configuration files."""
    config_dir = Path("config")
    
    if not (config_dir / "config.yaml").exists():
        if (config_dir / "config.example.yaml").exists():
            shutil.copy(
                config_dir / "config.example.yaml",
                config_dir / "config.yaml"
            )
            print("Created config.yaml from example")
        else:
            print("Warning: config.example.yaml not found")
    
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("Created .env from example")
        else:
            print("Warning: .env.example not found")


def main():
    """Main setup function."""
    print("Setting up Islamic RAG system...")
    
    setup_directories()
    setup_config()
    
    print("\nSetup complete!")
    print("\nNext steps:")
    print("1. Edit config/config.yaml with your settings")
    print("2. Edit .env with your API keys")
    print("3. Start Qdrant: docker-compose up qdrant -d")
    print("4. Run the system: python -m islamicRAG.api.main")


if __name__ == "__main__":
    main()