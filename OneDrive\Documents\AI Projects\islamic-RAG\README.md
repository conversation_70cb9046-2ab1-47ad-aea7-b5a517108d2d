# Islamic RAG System

A production-ready Retrieval-Augmented Generation system designed specifically for Islamic data sources including Quran verses, Ahadis, and Islamic scholarly texts.

## Features

- **Specialized for Islamic Content**: Optimized chunking for verses, hadith structure
- **High-Performance Vector Store**: Qdrant for scalable similarity search
- **Arabic-Optimized Embeddings**: Multilingual models with Arabic specialization
- **Production-Ready**: Comprehensive error handling, logging, and monitoring
- **Flexible Data Pipeline**: Support for multiple Islamic text formats

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure environment:
```bash
cp config/config.example.yaml config/config.yaml
# Edit config.yaml with your API keys and settings
```

3. Initialize the system:
```python
from islamicRAG import IslamicRAG

rag = IslamicRAG()
rag.initialize()
```

## Architecture

- `core/`: Core RAG components
- `embeddings/`: Embedding models and utilities
- `vector_store/`: Vector database operations
- `chunking/`: Islamic text-aware chunking strategies
- `data_pipeline/`: Data ingestion and processing
- `api/`: REST API endpoints
- `config/`: Configuration management

## Directory structure

```bash
islamicRAG/
├── islamicRAG/           # Main package
│   ├── core/            # Core system components
│   ├── embeddings/      # Embedding management
│   ├── vector_store/    # Vector database operations
│   ├── chunking/        # Islamic text chunking
│   ├── llm/            # Gemini client
│   ├── data_pipeline/   # Data ingestion
│   └── api/            # REST API
├── config/             # Configuration files
├── scripts/            # Setup and utility scripts
├── tests/              # Test suite
├── docker-compose.yml  # Container orchestration
├── Dockerfile         # Container definition
└── requirements.txt   # Dependencies
```